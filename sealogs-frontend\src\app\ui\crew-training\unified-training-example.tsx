'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { useLazyQuery } from '@apollo/client'
import {
    TRAINING_SESSIONS,
    READ_TRAINING_SESSION_DUES,
} from '@/app/lib/graphQL/query'
import { UnifiedTrainingTable } from './unified-training-table'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { Card } from '@/components/ui'
import { H2 } from '@/components/ui/typography'
import { mergeAndSortCrewTrainingData } from '@/app/ui/crew-training/utils/crew-training-utils'
import { UnifiedTrainingFilter } from '@/components/filter/unified-training-filter'
import { useUnifiedTrainingFilters } from './hooks/useUnifiedTrainingFilters'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { Loader2 } from 'lucide-react'

interface UnifiedTrainingExampleProps {
    vesselId?: number
    memberId?: number
    isVesselView?: boolean
}

export const UnifiedTrainingExample = ({
    vesselId = 0,
    memberId = 0,
    isVesselView = false,
}: UnifiedTrainingExampleProps) => {
    const [isLoading, setIsLoading] = useState(true)
    const [trainingSessionDues, setTrainingSessionDues] = useState<any[]>([])
    const [completedTrainingList, setCompletedTrainingList] = useState<any[]>(
        [],
    )
    const includeCompleted = true // Always include completed in unified view

    const [permissions, setPermissions] = useState<any>(false)

    const { getVesselWithIcon } = useVesselIconData()

    // Initialize permissions
    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    // Query for training session dues (overdue/upcoming)
    const [queryTrainingSessionDues, { loading: duesLoading }] = useLazyQuery(
        READ_TRAINING_SESSION_DUES,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingSessionDues.nodes || []

                setTrainingSessionDues(data)
            },
            onError: (error: any) => {
                console.error('Error loading training session dues:', error)
            },
        },
    )

    // Query for completed training sessions
    const [queryCompletedTraining, { loading: completedLoading }] =
        useLazyQuery(TRAINING_SESSIONS, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingSessions.nodes || []

                setCompletedTrainingList(data)
            },
            onError: (error: any) => {
                console.error('Error loading completed training:', error)
            },
        })

    // Load training session dues function
    const loadTrainingSessionDues = useCallback(
        async (filter: any) => {
            const duesFilter: any = {}
            if (memberId && memberId > 0) {
                duesFilter.memberID = { eq: +memberId }
            }
            if (vesselId && vesselId > 0) {
                duesFilter.vesselID = { eq: +vesselId }
            }
            if (filter.vesselID) {
                duesFilter.vesselID = filter.vesselID
            }
            if (filter.trainingTypes) {
                duesFilter.trainingTypeID = {
                    eq: filter.trainingTypes.id.contains,
                }
            }
            if (filter.members) {
                duesFilter.memberID = { eq: filter.members.id.contains }
            }
            if (filter.date) {
                duesFilter.dueDate = filter.date
            } else {
                duesFilter.dueDate = { ne: null }
            }

            await queryTrainingSessionDues({
                variables: {
                    filter: duesFilter,
                },
            })
        },
        [memberId, vesselId, queryTrainingSessionDues],
    )

    // Load completed training function
    const loadTrainingList = useCallback(
        async (startPage: number = 0, searchFilter: any = {}) => {
            const completedFilter: any = {}
            if (vesselId && vesselId > 0) {
                completedFilter.vesselID = { eq: +vesselId }
            }
            if (searchFilter.vesselID) {
                completedFilter.vesselID = searchFilter.vesselID
            }

            await queryCompletedTraining({
                variables: {
                    filter: completedFilter,
                    offset: startPage * 20, // Use 20 items per page to match pageSize
                    limit: 20,
                },
            })
        },
        [vesselId, queryCompletedTraining],
    )

    // Memoize the unified data calculation to prevent unnecessary recalculations
    const unifiedData = useMemo(() => {
        const startTime = performance.now()

        const result = mergeAndSortCrewTrainingData({
            trainingSessionDues,
            completedTrainingList,
            getVesselWithIcon,
            includeCompleted,
        })

        const endTime = performance.now()
        const mergeTime = endTime - startTime

        return result
    }, [
        trainingSessionDues,
        completedTrainingList,
        getVesselWithIcon,
        includeCompleted,
    ])

    // Use unified training filters hook with client-side filtering
    const { handleFilterChange, filteredData } = useUnifiedTrainingFilters({
        initialFilter: {},
        unifiedData,
    })

    // Memoize the filter change handler to prevent unnecessary re-renders
    const memoizedHandleFilterChange = useCallback(
        (filterData: { type: string; data: any }) => {
            handleFilterChange(filterData)
        },
        [handleFilterChange],
    )

    // Simplified data loading without server-side filtering
    const loadData = useCallback(async () => {
        setIsLoading(true)

        // Load training session dues without filters (client-side filtering will handle this)
        const duesFilter: any = {}
        if (vesselId && vesselId > 0) {
            duesFilter.vesselID = { eq: +vesselId }
        }
        if (memberId && +memberId > 0) {
            duesFilter.members = { id: { contains: +memberId } }
        }
        await loadTrainingSessionDues(duesFilter)

        // Load completed training without filters (client-side filtering will handle this)
        const completedFilter: any = {}
        if (vesselId && vesselId > 0) {
            completedFilter.vesselID = { eq: +vesselId }
        }
        await loadTrainingList(0, completedFilter)

        setIsLoading(false)
    }, [vesselId, memberId, loadTrainingSessionDues, loadTrainingList])

    // Load data on component mount
    useEffect(() => {
        loadData()
    }, [loadData])

    // Enhanced loading state management
    const hasOverdueUpcomingData =
        trainingSessionDues && trainingSessionDues.length > 0
    const hasCompletedData =
        completedTrainingList && completedTrainingList.length > 0

    // Create detailed loading status
    const getLoadingStatus = () => {
        const statuses = []
        if (duesLoading) statuses.push('overdue/upcoming training')
        if (completedLoading) statuses.push('completed training')
        if (isLoading) statuses.push('training data')
        return statuses
    }

    // Comprehensive loading component
    const LoadingIndicator = ({ status }: { status: string[] }) => (
        <div className="flex items-center justify-center py-8">
            <div className="text-center space-y-3">
                <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                <div className="space-y-1">
                    <p className="text-sm font-medium">
                        Loading training data...
                    </p>
                    {status.length > 0 && (
                        <p className="text-xs text-muted-foreground">
                            Currently loading: {status.join(', ')}
                        </p>
                    )}
                </div>
            </div>
        </div>
    )

    // Partial loading component for when some data is available
    const PartialLoadingIndicator = ({ status }: { status: string[] }) => (
        <div className="flex items-center justify-center py-4 bg-muted/30 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Loading {status.join(', ')}...</span>
            </div>
        </div>
    )

    // Check permissions
    if (
        !permissions ||
        (!hasPermission('EDIT_TRAINING', permissions) &&
            !hasPermission('VIEW_TRAINING', permissions) &&
            !hasPermission('RECORD_TRAINING', permissions) &&
            !hasPermission('VIEW_MEMBER_TRAINING', permissions))
    ) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    return (
        <div className="w-full space-y-6">
            {/* Filter Section */}
            <Card>
                <UnifiedTrainingFilter
                    memberId={memberId}
                    onChange={memoizedHandleFilterChange}
                />
            </Card>

            <Card className="p-6">
                <div className="space-y-6">
                    {/* Header with statistics */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <H2>Crew Training Overview</H2>
                            <p className="text-sm text-muted-foreground mt-1">
                                Unified view of all training activities
                            </p>
                        </div>
                    </div>
                    {/* Unified training table with enhanced loading states */}
                    <div>
                        {isLoading &&
                        !hasOverdueUpcomingData &&
                        !hasCompletedData ? (
                            // Full loading state when no data is available
                            <LoadingIndicator status={getLoadingStatus()} />
                        ) : isLoading &&
                          (hasOverdueUpcomingData || hasCompletedData) ? (
                            // Partial loading state when some data is available
                            <div className="space-y-4">
                                <UnifiedTrainingTable
                                    unifiedData={filteredData}
                                    getVesselWithIcon={getVesselWithIcon}
                                    includeCompleted={includeCompleted}
                                    memberId={memberId}
                                    isVesselView={isVesselView}
                                    showToolbar={false}
                                    pageSize={20}
                                />
                                <PartialLoadingIndicator
                                    status={getLoadingStatus()}
                                />
                            </div>
                        ) : (
                            // Normal state with data
                            <UnifiedTrainingTable
                                unifiedData={filteredData}
                                getVesselWithIcon={getVesselWithIcon}
                                includeCompleted={includeCompleted}
                                memberId={memberId}
                                isVesselView={isVesselView}
                                showToolbar={false}
                                pageSize={20}
                            />
                        )}
                    </div>

                    {/* Loading status information */}
                    {isLoading &&
                        (hasOverdueUpcomingData || hasCompletedData) && (
                            <div className="text-xs text-muted-foreground">
                                <p>
                                    Some data is still loading. The table will
                                    update automatically when new data becomes
                                    available.
                                </p>
                            </div>
                        )}

                    {/* No data state */}
                    {!isLoading && unifiedData.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                            <p className="text-sm">
                                No training data available
                            </p>
                            <p className="text-xs mt-1">
                                Try adjusting your filters or refresh the data
                            </p>
                        </div>
                    )}
                </div>
            </Card>
        </div>
    )
}

export default UnifiedTrainingExample
