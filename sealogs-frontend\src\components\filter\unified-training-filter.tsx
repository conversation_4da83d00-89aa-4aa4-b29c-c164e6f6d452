'use client'

import { useEffect, useState, useCallback, memo } from 'react'
import VesselDropdown from './components/vessel-dropdown'
import TrainingTypeDropdown from './components/training-type-dropdown'
import CrewDropdown from './components/crew-dropdown/crew-dropdown'
import DateRange from '../DateRange'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion'
import { useBreakpoints } from '../hooks/useBreakpoints'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'

// TypeScript interfaces for unified training filter
export interface UnifiedTrainingFilterProps {
    onChange: (filterData: { type: string; data: any }) => void
    vesselIdOptions?: any[]
    trainingTypeIdOptions?: any[]
    memberId?: number
    trainerIdOptions?: any[]
    memberIdOptions?: any[]
}

export interface UnifiedTrainingFilterData {
    vesselID?: { eq?: number; in?: number[] }
    trainingTypes?: { id: { contains?: number; in?: number[] } }
    trainer?: { id: { eq?: number; in?: number[] } }
    members?: { id: { eq?: number; in?: number[] } }
    date?: { gte: Date; lte: Date }
    category?: 'all' | 'overdue' | 'upcoming' | 'completed'
}

const UnifiedTrainingFilterComponent = ({
    onChange,
    vesselIdOptions = [],
    trainingTypeIdOptions = [],
    memberId = 0,
    trainerIdOptions = [],
    memberIdOptions = [],
}: UnifiedTrainingFilterProps) => {
    const [selectedCategory, setSelectedCategory] = useState<string>('all')

    // Memoize the dropdown change handler to prevent unnecessary re-renders
    const handleDropdownChange = useCallback(
        (type: string, data: any) => {
            console.log(
                '🔍 [UnifiedTrainingFilter] handleDropdownChange called:',
                {
                    type,
                    data,
                    dataType: typeof data,
                    isArray: Array.isArray(data),
                },
            )
            onChange({ type, data })
        },
        [onChange],
    )

    // Memoize the category change handler
    const handleCategoryChange = useCallback(
        (value: string) => {
            setSelectedCategory(value)
            handleDropdownChange('category', value)
        },
        [handleDropdownChange],
    )

    const bp = useBreakpoints()

    const filterContent = (
        <div className="grid xs:grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2.5">
            {/* Category Filter - New for unified view */}
            <Select
                value={selectedCategory}
                onValueChange={handleCategoryChange}>
                <SelectTrigger className="w-full">
                    <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="overdue">
                        <span className="flex items-center gap-2">
                            <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                            Overdue
                        </span>
                    </SelectItem>
                    <SelectItem value="upcoming">
                        <span className="flex items-center gap-2">
                            <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                            Upcoming
                        </span>
                    </SelectItem>
                    <SelectItem value="completed">
                        <span className="flex items-center gap-2">
                            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                            Completed
                        </span>
                    </SelectItem>
                </SelectContent>
            </Select>

            {/* Date Range Filter - Always visible in unified view */}
            <DateRange
                onChange={(data: any) =>
                    handleDropdownChange('dateRange', data)
                }
                clearable
            />

            {/* Training Type Filter - Always visible */}
            <TrainingTypeDropdown
                isClearable={true}
                onChange={(data: any) =>
                    handleDropdownChange('trainingType', data)
                }
                trainingTypeIdOptions={trainingTypeIdOptions}
            />

            {/* Vessel Filter - Always visible */}
            <VesselDropdown
                isClearable={true}
                onChange={(data: any) => handleDropdownChange('vessel', data)}
                vesselIdOptions={vesselIdOptions}
            />

            {/* Trainer Filter - Always visible */}
            <CrewDropdown
                label=""
                placeholder="Trainer"
                isClearable={true}
                multi
                controlClasses="filter"
                onChange={(data: any) => handleDropdownChange('trainer', data)}
                filterByTrainingSessionMemberId={memberId}
                trainerIdOptions={trainerIdOptions}
            />

            {/* Member/Crew Filter - Always visible */}
            <CrewDropdown
                isClearable={true}
                label=""
                multi
                controlClasses="filter"
                placeholder="Crew"
                onChange={(data: any) => handleDropdownChange('member', data)}
                filterByTrainingSessionMemberId={memberId}
                memberIdOptions={memberIdOptions}
            />
        </div>
    )

    return (
        <>
            {bp.phablet ? (
                filterContent
            ) : (
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="unified-training-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
            )}
        </>
    )
}

// Export memoized component for better performance
export const UnifiedTrainingFilter = memo(UnifiedTrainingFilterComponent)
